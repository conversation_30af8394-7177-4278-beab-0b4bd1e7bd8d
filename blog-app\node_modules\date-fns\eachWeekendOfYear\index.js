"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = eachWeekendOfYear;
var _index = _interopRequireDefault(require("../eachWeekendOfInterval/index.js"));
var _index2 = _interopRequireDefault(require("../endOfYear/index.js"));
var _index3 = _interopRequireDefault(require("../startOfYear/index.js"));
var _index4 = _interopRequireDefault(require("../_lib/requiredArgs/index.js"));
/**
 * @name eachWeekendOfYear
 * @category Year Helpers
 * @summary List all the Saturdays and Sundays in the year.
 *
 * @description
 * Get all the Saturdays and Sundays in the year.
 *
 * @param {Date|Number} date - the given year
 * @returns {Date[]} an array containing all the Saturdays and Sundays
 * @throws {TypeError} 1 argument required
 * @throws {RangeError} The passed date is invalid
 *
 * @example
 * // Lists all Saturdays and Sundays in the year
 * const result = eachWeekendOfYear(new Date(2020, 1, 1))
 * //=> [
 * //   Sat Jan 03 2020 00:00:00,
 * //   Sun Jan 04 2020 00:00:00,
 * //   ...
 * //   Sun Dec 27 2020 00:00:00
 * // ]
 * ]
 */
function eachWeekendOfYear(dirtyDate) {
  (0, _index4.default)(1, arguments);
  var startDate = (0, _index3.default)(dirtyDate);
  var endDate = (0, _index2.default)(dirtyDate);
  return (0, _index.default)({
    start: startDate,
    end: endDate
  });
}
module.exports = exports.default;