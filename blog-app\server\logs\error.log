{
  service: 'pdf-reader-api',
  level: 'error',
  message: 'Database seeding failed: Database not initialized',
  stack: 'Error: Database not initialized\n' +
    '    at C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:162:14\n' +
    '    at new Promise (<anonymous>)\n' +
    '    at runQuery (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:160:10)\n' +
    '    at seedDatabase (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\scripts\\seedDb.js:11:11)',
  timestamp: '2025-06-13 18:46:07'
}
{
  message: '401 - Invalid email or password - /api/auth/login - POST - ::1',
  level: 'error',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:16'
}
{
  message: '401 - Invalid email or password - /api/auth/login - POST - ::1',
  level: 'error',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 21:00:14'
}
