@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 text-white font-medium px-4 py-2 rounded-lg border-0 cursor-pointer no-underline inline-block transition-colors duration-200 hover:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 font-medium px-4 py-2 rounded-lg border-0 cursor-pointer no-underline inline-block transition-colors duration-200 hover:bg-gray-300;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm p-6;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
