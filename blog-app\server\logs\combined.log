{
  message: 'Starting database initialization...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:55'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:55'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:57'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:06'
}
{
  service: 'pdf-reader-api',
  level: 'error',
  message: 'Database seeding failed: Database not initialized',
  stack: 'Error: Database not initialized\n' +
    '    at C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:162:14\n' +
    '    at new Promise (<anonymous>)\n' +
    '    at runQuery (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:160:10)\n' +
    '    at seedDatabase (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\scripts\\seedDb.js:11:11)',
  timestamp: '2025-06-13 18:46:07'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:34'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: '::1 - - [13/Jun/2025:15:49:35 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:35'
}
{
  message: '::1 - - [13/Jun/2025:15:49:44 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:44'
}
{
  message: '::1 - - [13/Jun/2025:15:49:46 +0000] "GET /api/docs HTTP/1.1" 200 718 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:46'
}
{
  message: '::1 - - [13/Jun/2025:15:49:46 +0000] "GET /favicon.ico HTTP/1.1" 404 101 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:46'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:38'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\pdfs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\covers',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\temp',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: '::1 - - [13/Jun/2025:16:16:48 +0000] "GET /api/docs HTTP/1.1" 200 718 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:48'
}
{
  message: '::1 - - [13/Jun/2025:16:16:49 +0000] "GET /favicon.ico HTTP/1.1" 404 101 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:49'
}
{
  message: '::1 - - [13/Jun/2025:16:18:20 +0000] "GET /api/docs HTTP/1.1" 304 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:18:20'
}
{
  message: '::1 - - [13/Jun/2025:16:29:19 +0000] "GET /api/docs HTTP/1.1" 304 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:29:19'
}
{
  message: '::1 - - [13/Jun/2025:17:10:36 +0000] "GET /api/books HTTP/1.1" 200 871 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:10:36'
}
{
  message: '::1 - - [13/Jun/2025:17:11:24 +0000] "GET /health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:24'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:37'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:37'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:37'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:37'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:42'
}
{
  message: '::1 - - [13/Jun/2025:17:11:54 +0000] "GET /api/books HTTP/1.1" 200 1648 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:11:54'
}
{
  message: '::1 - - [13/Jun/2025:17:12:18 +0000] "GET /api/books/meta/categories HTTP/1.1" 200 162 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:12:18'
}
{
  message: '::1 - - [13/Jun/2025:17:13:09 +0000] "GET /health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:13:09'
}
{
  message: '::1 - - [13/Jun/2025:17:15:43 +0000] "GET /health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:15:43'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:04'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:28:50'
}
{
  message: '::1 - - [13/Jun/2025:17:29:23 +0000] "GET /api/books/meta/categories HTTP/1.1" 200 162 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:23'
}
{
  message: '::1 - - [13/Jun/2025:17:29:24 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 200 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:24'
}
{
  message: '::1 - - [13/Jun/2025:17:29:24 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:24'
}
{
  message: '::1 - - [13/Jun/2025:17:29:24 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:24'
}
{
  message: '::1 - - [13/Jun/2025:17:29:41 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:41'
}
{
  message: '::1 - - [13/Jun/2025:17:29:41 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:41'
}
{
  message: '::1 - - [13/Jun/2025:17:29:42 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:42'
}
{
  message: '::1 - - [13/Jun/2025:17:29:42 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:42'
}
{
  message: '::1 - - [13/Jun/2025:17:29:47 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:47'
}
{
  message: '::1 - - [13/Jun/2025:17:29:56 +0000] "GET /api/books HTTP/1.1" 200 1648 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:29:56'
}
{
  message: '::1 - - [13/Jun/2025:17:30:04 +0000] "GET /api/books/meta/categories HTTP/1.1" 200 162 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:30:04'
}
{
  message: '::1 - - [13/Jun/2025:17:40:03 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:03'
}
{
  message: '::1 - - [13/Jun/2025:17:40:03 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:03'
}
{
  message: '::1 - - [13/Jun/2025:17:40:03 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:03'
}
{
  message: '::1 - - [13/Jun/2025:17:40:03 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:03'
}
{
  message: '::1 - - [13/Jun/2025:17:40:08 +0000] "GET /api/books/6 HTTP/1.1" 200 554 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:08'
}
{
  message: '::1 - - [13/Jun/2025:17:40:08 +0000] "GET /api/books/6 HTTP/1.1" 200 554 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:08'
}
{
  message: '::1 - - [13/Jun/2025:17:40:15 +0000] "GET /api/books/6 HTTP/1.1" 200 554 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:15'
}
{
  message: '::1 - - [13/Jun/2025:17:40:15 +0000] "GET /api/books/6 HTTP/1.1" 200 554 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:15'
}
{
  message: '::1 - - [13/Jun/2025:17:40:25 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:25'
}
{
  message: '::1 - - [13/Jun/2025:17:40:25 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 200 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:25'
}
{
  message: '::1 - - [13/Jun/2025:17:40:25 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:25'
}
{
  message: '::1 - - [13/Jun/2025:17:40:25 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:40:25'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:41:53'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:50:11'
}
{
  message: '::1 - - [13/Jun/2025:17:52:31 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:52:31'
}
{
  message: 'User logged in: <EMAIL>',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:53:06'
}
{
  message: '::1 - - [13/Jun/2025:17:53:06 +0000] "POST /api/auth/login HTTP/1.1" 200 673 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:53:06'
}
{
  message: 'User logged out: <EMAIL>',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:53:20'
}
{
  message: '::1 - - [13/Jun/2025:17:53:20 +0000] "POST /api/users/logout HTTP/1.1" 200 50 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:53:20'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:15'
}
{
  message: '401 - Invalid email or password - /api/auth/login - POST - ::1',
  level: 'error',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:16'
}
{
  message: '::1 - - [13/Jun/2025:17:56:16 +0000] "POST /api/auth/login HTTP/1.1" 401 295 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:16'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:27'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:56:35'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:31'
}
{
  message: '::1 - - [13/Jun/2025:17:58:44 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:58:44'
}
{
  message: 'User logged in: <EMAIL>',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:27'
}
{
  message: '::1 - - [13/Jun/2025:17:59:27 +0000] "POST /api/auth/login HTTP/1.1" 200 690 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:27'
}
{
  message: '::1 - - [13/Jun/2025:17:59:35 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:35'
}
{
  message: '::1 - - [13/Jun/2025:17:59:35 +0000] "GET /api/books/meta/categories HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:35'
}
{
  message: '::1 - - [13/Jun/2025:17:59:36 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 200 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:36'
}
{
  message: '::1 - - [13/Jun/2025:17:59:36 +0000] "GET /api/books?search=&category=&page=1&limit=12 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:36'
}
{
  message: 'User logged out: <EMAIL>',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:56'
}
{
  message: '::1 - - [13/Jun/2025:17:59:56 +0000] "POST /api/users/logout HTTP/1.1" 200 50 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 20:59:56'
}
{
  message: '401 - Invalid email or password - /api/auth/login - POST - ::1',
  level: 'error',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 21:00:14'
}
{
  message: '::1 - - [13/Jun/2025:18:00:14 +0000] "POST /api/auth/login HTTP/1.1" 401 295 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 21:00:14'
}
