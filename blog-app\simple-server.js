const express = require('express');
const path = require('path');

const app = express();
const PORT = 5173;

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'client-web/dist')));

// Handle React Router (return `index.html` for all non-API routes)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client-web/dist/index.html'));
});

app.listen(PORT, () => {
  console.log(`Frontend server running on http://localhost:${PORT}`);
});
