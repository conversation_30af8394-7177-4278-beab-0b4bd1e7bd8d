import { useState } from 'react';

const AnnotationPanel = ({ 
  annotations, 
  currentPage, 
  onSaveAnnotation, 
  onDeleteAnnotation,
  onGoToPage 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [newNote, setNewNote] = useState('');
  const [selectedType, setSelectedType] = useState('note');

  const currentPageAnnotations = annotations.filter(ann => ann.page === currentPage);
  const allAnnotations = annotations.sort((a, b) => a.page - b.page);

  const handleSaveNote = async () => {
    if (!newNote.trim()) return;

    try {
      await onSaveAnnotation({
        type: selectedType,
        content: newNote,
        position: { x: 0, y: 0 }, // Default position
      });
      setNewNote('');
    } catch (error) {
      console.error('Failed to save annotation:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAnnotationIcon = (type) => {
    switch (type) {
      case 'highlight':
        return (
          <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        );
      case 'bookmark':
        return (
          <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
          </svg>
        );
    }
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 bg-primary-600 text-white p-3 rounded-l-lg shadow-lg hover:bg-primary-700 transition-colors z-40"
        title="Annotations"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </button>

      {/* Annotation Panel */}
      <div className={`fixed right-0 top-0 h-full w-80 bg-white shadow-xl transform transition-transform duration-300 z-30 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Annotations</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="p-1 rounded-md hover:bg-gray-100"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Add New Annotation */}
          <div className="p-4 border-b border-gray-200">
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add Note to Page {currentPage}
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full mb-2 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="note">Note</option>
                <option value="highlight">Highlight</option>
                <option value="bookmark">Bookmark</option>
              </select>
              <textarea
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                placeholder="Enter your note..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                rows="3"
              />
            </div>
            <button
              onClick={handleSaveNote}
              disabled={!newNote.trim()}
              className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add Annotation
            </button>
          </div>

          {/* Current Page Annotations */}
          {currentPageAnnotations.length > 0 && (
            <div className="p-4 border-b border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Page {currentPage} ({currentPageAnnotations.length})
              </h4>
              <div className="space-y-3">
                {currentPageAnnotations.map((annotation) => (
                  <div key={annotation.id} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getAnnotationIcon(annotation.type)}
                        <span className="text-xs font-medium text-gray-600 capitalize">
                          {annotation.type}
                        </span>
                      </div>
                      <button
                        onClick={() => onDeleteAnnotation(annotation.id)}
                        className="text-red-500 hover:text-red-700"
                        title="Delete annotation"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                    <p className="text-sm text-gray-800">{annotation.content}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(annotation.created_at)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* All Annotations */}
          <div className="flex-1 overflow-y-auto p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              All Annotations ({allAnnotations.length})
            </h4>
            {allAnnotations.length > 0 ? (
              <div className="space-y-3">
                {allAnnotations.map((annotation) => (
                  <div 
                    key={annotation.id} 
                    className="bg-gray-50 rounded-lg p-3 cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => onGoToPage(annotation.page)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getAnnotationIcon(annotation.type)}
                        <span className="text-xs font-medium text-gray-600">
                          Page {annotation.page}
                        </span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteAnnotation(annotation.id);
                        }}
                        className="text-red-500 hover:text-red-700"
                        title="Delete annotation"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                    <p className="text-sm text-gray-800 line-clamp-2">{annotation.content}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(annotation.created_at)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <p className="text-sm text-gray-500">No annotations yet</p>
                <p className="text-xs text-gray-400 mt-1">Add notes, highlights, or bookmarks</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-25 z-20"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default AnnotationPanel;
